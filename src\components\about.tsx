import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Palette, Code, Shield, Lightbulb } from 'lucide-react'

export function About() {
  const highlights = [
    {
      icon: Palette,
      title: "Creative Design",
      description: "Almost 10 years of graphic design expertise with a keen eye for aesthetics and user experience"
    },
    {
      icon: Code,
      title: "Software Development",
      description: "2+ years building robust, scalable applications with modern technologies"
    },
    {
      icon: Shield,
      title: "Security Focus",
      description: "Specialized in end-to-end encryption and highly secure system architecture"
    },
    {
      icon: Lightbulb,
      title: "Innovation Driven",
      description: "Passionate about exploring new technologies and pushing creative boundaries"
    }
  ]

  return (
    <section id="about" className="py-20 bg-muted/30 flex items-center justify-center">
      <div className="container">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4">About Me</Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Bridging Creativity & Technology
            </h2>
            <p className="text-lg text-muted-foreground leading-relaxed">
              With a unique blend of artistic vision and technical expertise, I create digital solutions 
              that are not only functional but also visually compelling. My journey spans from traditional 
              graphic design to cutting-edge software development, always with a focus on security and innovation.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {highlights.map((highlight, index) => (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="p-3 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                      <highlight.icon className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg mb-2">{highlight.title}</h3>
                      <p className="text-muted-foreground">{highlight.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border-none">
            <CardContent className="p-8">
              <div className="text-center">
                <h3 className="text-2xl font-bold mb-4">My Philosophy</h3>
                <p className="text-lg text-muted-foreground leading-relaxed">
                  "Great software is like great art - it should be both beautiful and functional. 
                  Every line of code is a brushstroke, every interface a canvas, and every user 
                  interaction a story waiting to be told."
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}