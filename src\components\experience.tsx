import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Calendar, MapPin } from 'lucide-react'

export function Experience() {
  const experiences = [
    {
      title: "Software Developer",
      company: "Current Role",
      location: "Remote",
      period: "2023 - Present",
      description: "Developing secure, end-to-end encrypted applications with focus on scalability and performance.",
      achievements: [
        "Built Team Announcements System with real-time notifications",
        "Developed Events Management System handling hundred concurrent users",
        "Created Fitness System with advanced tracking capabilities",
        "Implemented Manufacturing Tracking System",
        "Designed App Center System for enterprise application management",
        "Built Portfolio Management Platform with advanced analytics"
      ],
      technologies: ["Angular", "React", "PhalconPHP", "Laravel", "MySQL", "AWS"]
    },
    {
      title: "Graphic Artist",
      company: "Freelance & Agency Work",
      location: "Various",
      period: "2016 - Present",
      description: "Creating compelling visual designs across digital and print media with focus on brand identity and user experience.",
      achievements: [
        "Created marketing materials reaching millions of users",
        "Managed creative projects from concept to completion",
        "Collaborated with development teams on design implementation"
      ],
      technologies: ["Adobe Creative Suite", "Figma", "UI/UX Design", "Brand Design"]
    }
  ]

  return (
    <section id="experience" className="py-20 flex items-center justify-center">
      <div className="container">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4">Experience</Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Professional Journey
            </h2>
            <p className="text-lg text-muted-foreground">
              A decade of creative excellence combined with cutting-edge technical expertise
            </p>
          </div>

          <div className="space-y-8">
            {experiences.map((exp, index) => (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div>
                      <CardTitle className="text-xl group-hover:text-primary transition-colors">
                        {exp.title}
                      </CardTitle>
                      <p className="text-lg font-medium text-muted-foreground">{exp.company}</p>
                    </div>
                    <div className="flex flex-col md:items-end gap-2">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4 mr-1" />
                        {exp.period}
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <MapPin className="h-4 w-4 mr-1" />
                        {exp.location}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-6">{exp.description}</p>
                  
                  <div className="mb-6">
                    <h4 className="font-semibold mb-3">Key Achievements:</h4>
                    <ul className="space-y-2">
                      {exp.achievements.map((achievement, i) => (
                        <li key={i} className="flex items-start">
                          <div className="w-2 h-2 rounded-full bg-primary mt-2 mr-3 flex-shrink-0" />
                          <span className="text-muted-foreground">{achievement}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-3">Technologies:</h4>
                    <div className="flex flex-wrap gap-2">
                      {exp.technologies.map((tech, i) => (
                        <Badge key={i} variant="outline" className="hover:bg-primary hover:text-primary-foreground transition-colors">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}