import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Linkedin, MapPin } from "lucide-react";

export function Contact() {
  return (
    <section
      id="contact"
      className="py-20 bg-muted/30 flex items-center justify-center"
    >
      <div className="container px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-16">
            <Badge variant="secondary" className="mb-4">
              Get In Touch
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Let's Work Together
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Ready to bring your ideas to life? I'm always excited to discuss
              new projects and opportunities. Let's create something amazing
              together.
            </p>
          </div>

          <div className="max-w-2xl mx-auto">
            {/* Contact Information */}
            <div className="space-y-8">
              <div>
                <h3 className="text-2xl font-bold mb-6">Contact Information</h3>
                <div className="space-y-6">
                  <div className="flex items-center justify-center space-x-4">
                    <div className="p-3 rounded-lg bg-primary/10">
                      <Linkedin className="h-5 w-5 text-primary" />
                    </div>
                    <div className="text-left">
                      <p className="font-medium">LinkedIn</p>
                      <a
                        href="https://www.linkedin.com/in/jundelbetinol"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline"
                      >
                        linkedin.com/in/jundelbetinol
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center justify-center space-x-4">
                    <div className="p-3 rounded-lg bg-primary/10">
                      <MapPin className="h-5 w-5 text-primary" />
                    </div>
                    <div className="text-left">
                      <p className="font-medium">Location</p>
                      <p className="text-muted-foreground">
                        Available for Remote Work
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border-none">
                <CardContent className="p-8">
                  <h4 className="font-semibold mb-3">Let's Connect!</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    I'm always open to discussing new opportunities,
                    collaborations, or just having a chat about technology and
                    design.
                  </p>
                  <div className="flex justify-center">
                    <Button size="lg" variant="outline" asChild>
                      <a
                        href="https://www.linkedin.com/in/jundelbetinol"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Linkedin className="h-4 w-4 mr-2" />
                        Connect on LinkedIn
                      </a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
