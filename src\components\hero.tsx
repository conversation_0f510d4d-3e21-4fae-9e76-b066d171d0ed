import { Button } from '@/components/ui/button'
import { <PERSON>D<PERSON>, Linkedin } from 'lucide-react'

export function Hero() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900" />
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />
      
      <div className="container relative z-10 text-center">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <img
              src="/src/assets/1699452624319.jpg"
              alt="Jundel Betinol"
              className="w-32 h-32 rounded-full mx-auto mb-6 border-4 border-white shadow-xl object-cover"
            />
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Jundel Betinol
          </h1>
          
          <p className="text-xl md:text-2xl text-muted-foreground mb-4">
            Graphic Artist & Software Developer
          </p>
          
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto leading-relaxed">
            Transforming creative vision into digital reality with almost 10 years in graphic design
            and 2+ years in software development. Specializing in secure, end-to-end encrypted solutions.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button size="lg" asChild className="group">
              <a href="https://www.linkedin.com/in/jundelbetinol" target="_blank" rel="noopener noreferrer">
                <Linkedin className="mr-2 h-4 w-4 group-hover:scale-110 transition-transform" />
                LinkedIn Connect
              </a>
            </Button>
          </div>
          
          <button
            onClick={() => scrollToSection('about')}
            className="animate-bounce inline-flex items-center text-muted-foreground hover:text-primary transition-colors"
          >
            <ArrowDown className="h-6 w-6" />
          </button>
        </div>
      </div>
    </section>
  )
}