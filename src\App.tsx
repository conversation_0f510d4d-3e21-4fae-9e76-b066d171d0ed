import { ThemeProvider } from '@/components/theme-provider'
import { Header } from '@/components/header'
import { <PERSON> } from '@/components/hero'
import { About } from '@/components/about'
import { Experience } from '@/components/experience'
import { Projects } from '@/components/projects'
import { Skills } from '@/components/skills'
import { Contact } from '@/components/contact'
import { Footer } from '@/components/footer'
import { useEffect } from 'react'

function App() {
  useEffect(() => {
    const sections = ['hero', 'about', 'experience', 'projects', 'skills', 'contact'];
    let currentSectionIndex = 0;
    let lastScrollTime = 0;
    const scrollDelay = 800; // ms

    const scrollToSection = (index: number) => {
      const targetId = sections[index];
      const element = document.getElementById(targetId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    };

    const handleWheel = (e: WheelEvent) => {
      const now = Date.now();
      if (now - lastScrollTime < scrollDelay) return;

      e.preventDefault();
      lastScrollTime = now;

      if (e.deltaY > 0 && currentSectionIndex < sections.length - 1) {
        // Scroll down
        currentSectionIndex++;
      } else if (e.deltaY < 0 && currentSectionIndex > 0) {
        // Scroll up
        currentSectionIndex--;
      }

      scrollToSection(currentSectionIndex);
    };

    window.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      window.removeEventListener('wheel', handleWheel);
    };
  }, []);


  return (
    <ThemeProvider defaultTheme="light" storageKey="portfolio-theme">
      <div className="min-h-screen bg-background font-sans antialiased">
        <Header />
        <main>
          <section id="hero"><Hero /></section>
          <section id="about"><About /></section>
          <section id="experience"><Experience /></section>
          <section id="projects"><Projects /></section>
          <section id="skills"><Skills /></section>
          <section id="contact"><Contact /></section>
        </main>
        <Footer />
      </div>
    </ThemeProvider>
  )
}

export default App